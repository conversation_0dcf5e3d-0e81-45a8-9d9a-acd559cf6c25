<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Servlet Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 40px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .servlet-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .servlet-card {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
            text-decoration: none;
            color: #333;
            transition: transform 0.2s;
        }
        .servlet-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            text-decoration: none;
            color: #333;
        }
        .servlet-card h3 {
            margin: 0 0 10px 0;
            color: #007bff;
        }
        .servlet-card p {
            margin: 0;
            font-size: 14px;
            color: #666;
        }
        .test-forms {
            margin-top: 30px;
        }
        .form-section {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .form-section h3 {
            margin-top: 0;
            color: #333;
        }
        form {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        input, textarea, button {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        button:hover {
            background-color: #0056b3;
        }
        .method-buttons {
            display: flex;
            gap: 10px;
        }
        .method-buttons button {
            flex: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Servlet Examples Test Page</h1>
        
        <div class="servlet-links">
            <a href="/simple" class="servlet-card">
                <h3>Simple Servlet</h3>
                <p>Basic servlet example with GET/POST support. Shows current time and request information.</p>
            </a>
            
            <a href="/user" class="servlet-card">
                <h3>User Management Servlet</h3>
                <p>Advanced servlet with form processing, data storage, and user management features.</p>
            </a>
            
            <a href="/hello-world" class="servlet-card">
                <h3>Hello World Servlet</h3>
                <p>Spring Boot registered servlet using ServletRegistrationBean configuration.</p>
            </a>
            
            <a href="/info" class="servlet-card">
                <h3>Request Info Servlet</h3>
                <p>Displays detailed HTTP request information including headers and parameters.</p>
            </a>

            <a href="/thymeleaf" class="servlet-card" style="border-left-color: #28a745;">
                <h3>Thymeleaf Examples</h3>
                <p>Modern template engine with natural templates, Spring integration, and advanced features.</p>
            </a>

            <a href="/jsp/home" class="servlet-card" style="border-left-color: #ffc107;">
                <h3>JSP Examples</h3>
                <p>JavaServer Pages with JSTL, scriptlets, form processing, and session management.</p>
            </a>
        </div>
        
        <div class="test-forms">
            <div class="form-section">
                <h3>Test Simple Servlet POST</h3>
                <form action="/simple" method="post">
                    <button type="submit">Send POST Request to Simple Servlet</button>
                </form>
            </div>
            
            <div class="form-section">
                <h3>Test User Registration</h3>
                <form action="/user" method="post">
                    <input type="text" name="name" placeholder="Enter your name" required>
                    <input type="email" name="email" placeholder="Enter your email" required>
                    <input type="number" name="age" placeholder="Enter your age" min="1" max="120" required>
                    <button type="submit">Register User</button>
                </form>
            </div>
            
            <div class="form-section">
                <h3>Test Info Servlet with Parameters</h3>
                <form action="/info" method="get">
                    <input type="text" name="param1" placeholder="Parameter 1" value="test-value-1">
                    <input type="text" name="param2" placeholder="Parameter 2" value="test-value-2">
                    <div class="method-buttons">
                        <button type="submit" formmethod="get">Send GET Request</button>
                        <button type="submit" formmethod="post">Send POST Request</button>
                    </div>
                </form>
            </div>
        </div>
        
        <div style="margin-top: 30px; padding: 20px; background-color: #e9ecef; border-radius: 8px;">
            <h3>About These Servlets</h3>
            <p>This page demonstrates different servlet implementation approaches:</p>
            <ul>
                <li><strong>@WebServlet annotation:</strong> Traditional servlet registration (SimpleServlet, UserServlet)</li>
                <li><strong>ServletRegistrationBean:</strong> Spring Boot configuration-based registration (HelloWorldServlet, InfoServlet)</li>
                <li><strong>HTTP Methods:</strong> Examples of handling both GET and POST requests</li>
                <li><strong>Form Processing:</strong> Demonstrates parameter extraction and validation</li>
                <li><strong>HTML Generation:</strong> Shows how to generate dynamic HTML responses</li>
            </ul>
        </div>
    </div>
</body>
</html>
