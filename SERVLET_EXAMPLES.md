# Servlet Examples

This project contains several servlet examples demonstrating different approaches to creating servlets in a Spring Boot application.

## Servlet Examples Included

### 1. SimpleServlet (`/simple`)
- **Location**: `src/main/java/com/example/learning/servlets/SimpleServlet.java`
- **URL Pattern**: `/simple`
- **Implementation**: Uses `@WebServlet` annotation
- **Features**:
  - Handles both GET and POST requests
  - Displays current time and request information
  - Simple HTML response with styling

### 2. UserServlet (`/user`)
- **Location**: `src/main/java/com/example/learning/servlets/UserServlet.java`
- **URL Pattern**: `/user`
- **Implementation**: Uses `@WebServlet` annotation
- **Features**:
  - User registration form
  - In-memory user storage
  - Form validation and error handling
  - HTML table display of registered users
  - XSS protection with HTML escaping

### 3. HelloWorldServlet (`/hello-world`)
- **Location**: `src/main/java/com/example/learning/config/ServletConfig.java`
- **URL Pattern**: `/hello-world`
- **Implementation**: Spring Boot `ServletRegistrationBean`
- **Features**:
  - Simple greeting message
  - Demonstrates Spring Boot servlet registration

### 4. InfoServlet (`/info`)
- **Location**: `src/main/java/com/example/learning/config/ServletConfig.java`
- **URL Pattern**: `/info`
- **Implementation**: Spring Boot `ServletRegistrationBean`
- **Features**:
  - Displays detailed HTTP request information
  - Shows all request headers
  - Useful for debugging and learning

## How to Run

1. **Start the application**:
   ```bash
   ./mvnw spring-boot:run
   ```
   Or on Windows:
   ```cmd
   mvnw.cmd spring-boot:run
   ```

2. **Access the test page**:
   Open your browser and go to: `http://localhost:8081/test.html`

3. **Test individual servlets**:
   - Simple Servlet: `http://localhost:8081/simple`
   - User Management: `http://localhost:8081/user`
   - Hello World: `http://localhost:8081/hello-world`
   - Request Info: `http://localhost:8081/info`

## Key Concepts Demonstrated

### 1. Servlet Registration Methods
- **@WebServlet annotation**: Traditional approach, requires `@ServletComponentScan` in main class
- **ServletRegistrationBean**: Spring Boot configuration approach

### 2. HTTP Methods
- **GET requests**: Display forms and information
- **POST requests**: Process form submissions

### 3. Request Processing
- Parameter extraction using `request.getParameter()`
- Request information access (headers, URI, method, etc.)
- Response content type setting

### 4. HTML Generation
- Dynamic HTML creation using `PrintWriter`
- CSS styling for better presentation
- Form creation and processing

### 5. Security Considerations
- HTML escaping to prevent XSS attacks
- Input validation and error handling

## Project Structure

```
src/main/java/com/example/learning/
├── LearningApplication.java          # Main Spring Boot application with @ServletComponentScan
├── servlets/
│   ├── SimpleServlet.java           # Basic servlet example
│   └── UserServlet.java             # Advanced servlet with form processing
├── config/
│   └── ServletConfig.java           # Spring Boot servlet configuration
└── controllers/
    └── DemoController.java          # Existing Spring MVC controller

src/main/resources/
├── static/
│   └── test.html                    # Test page for all servlets
└── application.properties           # Application configuration
```

## Configuration Notes

1. **@ServletComponentScan**: Added to `LearningApplication.java` to enable scanning for servlet annotations
2. **Server Port**: Configured to run on port 8081 (see `application.properties`)
3. **Static Resources**: The `test.html` file is served from `src/main/resources/static/`

## Testing the Servlets

The `test.html` page provides:
- Links to all servlet endpoints
- Forms to test POST requests
- Parameter testing for the info servlet
- Visual examples of different servlet responses

## Next Steps

You can extend these examples by:
- Adding database integration
- Implementing session management
- Adding authentication and authorization
- Creating more complex form processing
- Adding JSON response support
- Implementing file upload functionality
